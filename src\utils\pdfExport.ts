import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface NotificationSettings {
  post_office_logo_url: string | null;
  admin_logo_url: string | null;
  admin_name: string | null;
  admin_email: string | null;
  admin_phone: string | null;
  admin_address: string | null;
  admin_tagline: string | null;
}

interface PDFExportOptions {
  title: string;
  content: string;
  filterSummary: Array<{ label: string; value: string; applied: boolean }>;
}

export const exportToPDF = async (options: PDFExportOptions) => {
  try {
    // Fetch notification settings for header data
    const { data: notificationSettings, error: settingsError } = await supabase
      .from('notification_settings')
      .select('*')
      .limit(1)
      .single();

    if (settingsError) {
      console.error('Error fetching notification settings:', settingsError);
    }

    // Get image URL from Supabase storage (same as ClientDetail.tsx)
    const getImageUrl = (path: string) => {
      if (!path) return null;
      const { data } = supabase.storage.from('documents').getPublicUrl(path);
      return data.publicUrl;
    };

    // Convert image URLs to base64 for PDF compatibility
    const convertImageToBase64 = async (imageUrl: string): Promise<string> => {
      try {
        if (!imageUrl) return '';

        // If it's a storage path, get the public URL first
        let fullUrl = imageUrl;
        if (!imageUrl.startsWith('http')) {
          const publicUrl = getImageUrl(imageUrl);
          fullUrl = publicUrl || `${window.location.origin}${imageUrl}`;
        }
        
        try {
          const response = await fetch(fullUrl);
          if (response.ok) {
            const blob = await response.blob();
            return new Promise((resolve) => {
              const reader = new FileReader();
              reader.onloadend = () => resolve(reader.result as string);
              reader.onerror = () => resolve('');
              reader.readAsDataURL(blob);
            });
          }
        } catch (fetchError) {
          console.log('Fetch failed, trying canvas method:', fetchError);
        }

        return new Promise((resolve) => {
          const img = new Image();
          img.onload = () => {
            try {
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              if (!ctx) {
                resolve('');
                return;
              }
              canvas.width = img.width;
              canvas.height = img.height;
              ctx.drawImage(img, 0, 0);
              resolve(canvas.toDataURL('image/png', 0.9));
            } catch (canvasError) {
              resolve('');
            }
          };
          img.onerror = () => resolve('');
          img.crossOrigin = 'anonymous';
          img.src = fullUrl;
          setTimeout(() => resolve(''), 10000);
        });
      } catch (error) {
        console.error('Error in convertImageToBase64:', error);
        return '';
      }
    };

    // Show loading toast
    toast({
      title: "Generating PDF",
      description: "Please wait while we prepare your report...",
    });

    // Convert logos to base64 using Supabase storage URLs
    const postOfficeLogoUrl = notificationSettings?.post_office_logo_url ? 
      getImageUrl(notificationSettings.post_office_logo_url) : 
      `${window.location.origin}/lovable-uploads/ab651bb9-5504-40da-ab30-1bd5fd7fdc5a.png`;
    
    const adminLogoUrl = notificationSettings?.admin_logo_url ? 
      getImageUrl(notificationSettings.admin_logo_url) : 
      `${window.location.origin}/lovable-uploads/e26f8b82-a2a2-46cc-a26e-fcf66ed47997.png`;

    const postOfficeLogoBase64 = await convertImageToBase64(postOfficeLogoUrl || '');
    const adminLogoBase64 = await convertImageToBase64(adminLogoUrl || '');

    const postOfficeLogo = postOfficeLogoBase64 || postOfficeLogoUrl;
    const adminLogo = adminLogoBase64 || adminLogoUrl;

    // Open a new window for the PDF report
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      toast({
        title: "Error",
        description: "Could not open print window. Please check your browser settings.",
        variant: "destructive",
      });
      return;
    }

    // Generate the full HTML document with styling
    const htmlContent = `
   <html>
<head>
  <title>Investment Report - ${new Date().toLocaleDateString()}</title>
  <style>
    @page {
      margin: 0.5in 0.75in;
      size: A4;
      @top-left {
        content: "${notificationSettings?.admin_name || 'InvestPro'} Report";
        font-size: 10px;
        color: #666;
        font-weight: 500;
      }
      @top-right {
        content: "Page " counter(page) " of " counter(pages);
        font-size: 10px;
        color: #666;
        font-weight: 500;
      }
      @bottom-center {
        content: "Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}";
        font-size: 9px;
        color: #999;
      }
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: Arial, sans-serif;
      color: #333;
      line-height: 1.4;
      background: #fff;
      padding: 10px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 20px 0;
      border-bottom: 2px solid #e0e0e0;
    }

    .header-left,
    .header-right {
      display: flex;
      align-items: flex-start;
      gap: 15px;
      width: 48%;
    }

    .logo img,
    .admin-logo img {
      max-height: 80px;
      max-width: 120px;
      object-fit: cover;
      display: block;
    }

    .post-office-info h3 {
      font-size: 18px;
      font-weight: bold;
      margin: 0;
    }

    .post-office-info p {
      font-size: 12px;
      color: #666;
      margin: 4px 0 0 0;
    }

    .admin-details {
      text-align: right;
    }

    .admin-details h3 {
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 4px 0;
    }

    .admin-details .tagline {
      font-style: italic;
      color: #4f46e5;
      font-size: 12px;
      margin: 0 0 6px 0;
    }

    .admin-details p {
      font-size: 11px;
      margin: 3px 0;
      line-height: 1.3;
    }

    .report-title {
      font-size: 20px;
      font-weight: 600;
      color: #4f46e5;
      margin: 20px 0 15px 0;
      border-bottom: 1px solid #e0e0e0;
      padding-bottom: 8px;
    }

    .filter-info {
      background: #f8f9fa;
      padding: 15px;
      margin-bottom: 20px;
      border-radius: 6px;
      border: 1px solid #e0e0e0;
    }

    .filter-info h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }

    .filter-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 6px;
    }

    .filter-item {
      padding: 4px 8px;
      font-size: 9px;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .summary-stats {
      display: flex;
      gap: 20px;
      margin-top: 20px;
      page-break-inside: avoid;
    }

    .stat-item {
      flex: 1;
      border: 1px solid #e0e0e0;
      padding: 10px;
      border-radius: 6px;
      background: #f9f9f9;
    }

    .stat-value {
      font-size: 18px;
      font-weight: 700;
      color: #4f46e5;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 10px;
      color: #666;
      font-weight: 500;
    }

    .report-table {
      width: 100%;
      margin-top: 20px;
      font-size: 10px;
      border-collapse: collapse;
      background: white;
      border: 1px solid #e0e0e0;
    }

    .report-table th,
    .report-table td {
      padding: 8px 10px;
      text-align: left;
      border-bottom: 1px solid #e0e0e0;
      vertical-align: middle;
    }

    .report-table th {
      background: #f8f9fa;
      color: #333;
      font-weight: 600;
    }

    .footer {
      margin-top: 30px;
      text-align: center;
      padding: 15px 0;
      border-top: 1px solid #e0e0e0;
      font-size: 9px;
      color: #999;
    }

    .footer p {
      margin: 3px 0;
    }

    @media print {
      body {
        margin: 0;
        padding: 10px;
      }

      .report-table {
        page-break-inside: auto;
        border: 1px solid #e0e0e0;
      }

      .report-table tr {
        page-break-inside: avoid;
        page-break-after: auto;
      }

      .header,
      .filter-info,
      .summary-stats {
        page-break-after: avoid;
      }
    }
  </style>
</head>

<body>
  <div class="header">
    <!-- Left Side -->
    <div class="header-left">
      <div class="logo">
        ${postOfficeLogoBase64 ? 
          `<img src="${postOfficeLogoBase64}" alt="Post Office Logo" />` : 
          `<img src="${postOfficeLogo}" alt="Post Office Logo" onerror="this.style.display='none';" />`}
      </div>
      <div class="admin-logo">
        ${adminLogoBase64 ? 
          `<img src="${adminLogoBase64}" alt="Admin Logo" />` : 
          `<img src="${adminLogo}" alt="Admin Logo" onerror="this.style.display='none';" />`}
      </div>
    </div>

    <!-- Right Side -->
    <div class="header-right">
      
      <div class="admin-details">
        <h3>${notificationSettings?.admin_name || 'InvestPro Investment Management'}</h3>
        ${notificationSettings?.admin_tagline ? `<p class="tagline">${notificationSettings.admin_tagline}</p>` : ''}
        ${notificationSettings?.admin_email ? `<p>Email: ${notificationSettings.admin_email}</p>` : ''}
        ${notificationSettings?.admin_phone ? `<p>Phone: ${notificationSettings.admin_phone}</p>` : ''}
        ${notificationSettings?.admin_address ? `<p>Address: ${notificationSettings.admin_address}</p>` : ''}
        <p>Generated: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
      </div>
    </div>
  </div>

        <h2 class="report-title">${options.title}</h2>

        <div class="filter-info">
          <h3>Report Filters</h3>
          <div class="filter-grid">
            ${options.filterSummary.map(filter => `
              <div class="filter-item">
                <span class="filter-label">${filter.label}:</span>
                <span class="filter-value">${filter.value}</span>
              </div>
            `).join('')}
          </div>
        </div>

        ${options.content}

        <div class="footer">
          <p>This report was generated by ${notificationSettings?.admin_name || 'InvestPro Investment Management System'}</p>
          <p>For any queries, please contact your system administrator</p>
          <p>© ${new Date().getFullYear()} ${notificationSettings?.admin_name || 'InvestPro'}. All rights reserved.</p>
        </div>
      </body>
      </html>
    `;

    // Add preload links for images
    let preloadLinks = '';
    if (postOfficeLogo) {
      preloadLinks += `<link rel="preload" href="${postOfficeLogo}" as="image">`;
    }
    if (adminLogo) {
      preloadLinks += `<link rel="preload" href="${adminLogo}" as="image">`;
    }
    
    const htmlWithPreload = htmlContent.replace('</head>', `${preloadLinks}</head>`);
    
    // Write the content to the new window
    printWindow.document.write(htmlWithPreload);
    printWindow.document.close();
    printWindow.focus();

    // Wait for content and images to load then print
    setTimeout(() => {
      const script = printWindow.document.createElement('script');
      script.textContent = `
        function loadImage(url) {
          return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(url);
            img.onerror = () => reject(new Error('Image failed to load: ' + url));
            img.src = url;
          });
        }
        
        function checkImagesLoaded() {
          const images = document.querySelectorAll('img');
          let allLoaded = true;
          let loadingPromises = [];
          
          for (let i = 0; i < images.length; i++) {
            if (!images[i].complete) {
              allLoaded = false;
              loadingPromises.push(loadImage(images[i].src));
            }
          }
          
          if (allLoaded) {
            window.print();
            setTimeout(() => window.close(), 500);
          } else {
            Promise.allSettled(loadingPromises).then(() => {
              setTimeout(() => {
                window.print();
                setTimeout(() => window.close(), 500);
              }, 200);
            });
          }
        }
        
        setTimeout(checkImagesLoaded, 500);
      `;
      printWindow.document.body.appendChild(script);
    }, 500);

    toast({
      title: "Report Generated",
      description: "Report has been generated. Please use your browser's print dialog to save as PDF.",
    });

  } catch (error) {
    console.error('Error generating PDF:', error);
    toast({
      title: "Error",
      description: "Failed to generate PDF report",
      variant: "destructive",
    });
  }
};